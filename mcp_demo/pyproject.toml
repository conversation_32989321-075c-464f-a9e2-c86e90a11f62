[project]
name = "mcp-demo"
version = "0.1.0"
description = "Add your description here"
authors = [
    { name = "<PERSON><PERSON>", email = "<EMAIL>" }
]
dependencies = [
    "fastmcp>=2.11.2",
    "httpx>=0.28.1",
    "uvicorn>=0.35.0",
]
readme = "README.md"
requires-python = ">= 3.11"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.rye]
managed = true
dev-dependencies = []

[tool.hatch.metadata]
allow-direct-references = true

[tool.hatch.build.targets.wheel]
packages = ["src/mcp_demo"]
