import http
from fastmcp import FastMCP
import uuid, httpx, time, httpx
from collections import Counter
from typing import Dict, Any
import re

mcp = FastMCP("ExampleMCP")


@mcp.tool
def add(a: int, b: int) -> int:
    """Add two numbers"""
    return a + b


@mcp.tool
def subtract(a: int, b: int) -> int:
    """Subtract two numbers"""
    return a - b


@mcp.tool
def echo(text: str) -> str:
    """Echo the same text (sanity check)"""
    return text


@mcp.tool
def now_iso() -> str:
    """Return the current time in ISO format"""
    return time.strftime("%Y-%m-%dT%H:%M:%S", time.gmtime())


@mcp.tool
def word_count(text: str) -> Dict[str, int]:
    """Count total and unique words (A–Z tokens)."""
    tokens = re.findall(r"[A-Za-z]+", text.lower())
    return {"words": len(tokens), "unique": len(set(tokens))}


@mcp.tool
def fetch_json(url: str, timeout_s: int = 10) -> dict:
    """
    GET a URL and return parsed JSON.
    Returns {"ok": false, "error": "..."} on failure.
    """
    try:
        with httpx.Client(timeout=timeout_s) as client:
            r = client.get(url, headers={"Accept": "application/json"})
            r.raise_for_status()
            return {"ok": True, "data": r.json()}
    except Exception as e:
        return {"ok": False, "error": str(e)}
