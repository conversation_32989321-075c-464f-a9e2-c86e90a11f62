import asyncio
from fastmcp import Client

MCP_URL = "http://mcp_server:8000/mcp"  # service-name from compose

async def main():
    c = Client(MCP_URL)
    async with c:
        await c.ping()
        tools = await c.list_tools()
        print("TOOLS:", [t.name for t in tools])

        r = await c.call_tool("add", {"a": 2, "b": 5})
        print("add(2,5) ->", r.data)

        r = await c.call_tool("echo", {"text": "hello mcp"})
        print("echo ->", r.data)

        r = await c.call_tool("now_iso", {})
        print("now_iso ->", r.data)

        r = await c.call_tool("word_count", {"text": "One fish two fish red fish blue fish"})
        print("word_count ->", r.data)

        # Optional: requires internet and a JSON endpoint
        # r = await c.call_tool("fetch_json", {"url": "https://api.github.com"})
        # print("fetch_json ->", r.data)

asyncio.run(main())
