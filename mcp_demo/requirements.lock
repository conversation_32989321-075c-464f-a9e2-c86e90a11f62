# generated by rye
# use `rye lock` or `rye sync` to update this lockfile
#
# last locked with the following flags:
#   pre: false
#   features: []
#   all-features: false
#   with-sources: false
#   generate-hashes: false
#   universal: false

-e file:.
annotated-types==0.7.0
    # via pydantic
anyio==4.10.0
    # via httpx
    # via mcp
    # via sse-starlette
    # via starlette
attrs==25.3.0
    # via cyclopts
    # via jsonschema
    # via referencing
authlib==1.6.1
    # via fastmcp
certifi==2025.8.3
    # via httpcore
    # via httpx
    # via requests
cffi==1.17.1
    # via cryptography
charset-normalizer==3.4.3
    # via requests
click==8.2.1
    # via uvicorn
cryptography==45.0.6
    # via authlib
cyclopts==3.22.5
    # via fastmcp
dnspython==2.7.0
    # via email-validator
docstring-parser==0.17.0
    # via cyclopts
docutils==0.22
    # via rich-rst
email-validator==2.2.0
    # via pydantic
exceptiongroup==1.3.0
    # via fastmcp
fastmcp==2.11.2
    # via mcp-demo
h11==0.16.0
    # via httpcore
    # via uvicorn
httpcore==1.0.9
    # via httpx
httpx==0.28.1
    # via fastmcp
    # via mcp
    # via mcp-demo
httpx-sse==0.4.1
    # via mcp
idna==3.10
    # via anyio
    # via email-validator
    # via httpx
    # via requests
isodate==0.7.2
    # via openapi-core
jsonschema==4.25.0
    # via mcp
    # via openapi-core
    # via openapi-schema-validator
    # via openapi-spec-validator
jsonschema-path==0.3.4
    # via openapi-core
    # via openapi-spec-validator
jsonschema-specifications==2025.4.1
    # via jsonschema
    # via openapi-schema-validator
lazy-object-proxy==1.11.0
    # via openapi-spec-validator
markdown-it-py==4.0.0
    # via rich
markupsafe==3.0.2
    # via werkzeug
mcp==1.12.4
    # via fastmcp
mdurl==0.1.2
    # via markdown-it-py
more-itertools==10.7.0
    # via openapi-core
openapi-core==0.19.5
    # via fastmcp
openapi-pydantic==0.5.1
    # via fastmcp
openapi-schema-validator==0.6.3
    # via openapi-core
    # via openapi-spec-validator
openapi-spec-validator==0.7.2
    # via openapi-core
parse==1.20.2
    # via openapi-core
pathable==0.4.4
    # via jsonschema-path
pycparser==2.22
    # via cffi
pydantic==2.11.7
    # via fastmcp
    # via mcp
    # via openapi-pydantic
    # via pydantic-settings
pydantic-core==2.33.2
    # via pydantic
pydantic-settings==2.10.1
    # via mcp
pygments==2.19.2
    # via rich
pyperclip==1.9.0
    # via fastmcp
python-dotenv==1.1.1
    # via fastmcp
    # via pydantic-settings
python-multipart==0.0.20
    # via mcp
pyyaml==6.0.2
    # via jsonschema-path
referencing==0.36.2
    # via jsonschema
    # via jsonschema-path
    # via jsonschema-specifications
requests==2.32.4
    # via jsonschema-path
rfc3339-validator==0.1.4
    # via openapi-schema-validator
rich==14.1.0
    # via cyclopts
    # via fastmcp
    # via rich-rst
rich-rst==1.3.1
    # via cyclopts
rpds-py==0.27.0
    # via jsonschema
    # via referencing
six==1.17.0
    # via rfc3339-validator
sniffio==1.3.1
    # via anyio
sse-starlette==3.0.2
    # via mcp
starlette==0.47.2
    # via mcp
typing-extensions==4.14.1
    # via anyio
    # via exceptiongroup
    # via openapi-core
    # via pydantic
    # via pydantic-core
    # via referencing
    # via starlette
    # via typing-inspection
typing-inspection==0.4.1
    # via pydantic
    # via pydantic-settings
urllib3==2.5.0
    # via requests
uvicorn==0.35.0
    # via mcp
    # via mcp-demo
werkzeug==3.1.1
    # via openapi-core
